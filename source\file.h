#define KEEP_ALIVE 5
#define IMAGE_TYPE "image"
#define CHANGE_TYPE "displacement"

#define DIC_FOLDER "../datafile/"
#define IMAGE_LIST_FILE "../datafile/orig/originalObservation.txt"
#define CAPTURE_SINGLE "./capture_single"
#define HOST_PORT_FILE "../datafile/dic/para/parameters.txt"
#define POINT_LIST_FILE "../datafile/dic/para/points_list.txt"
#define IMAGE_FOLDER "../datafile/orig/"
#define MASTER_IMAGE_FOLDER "../datafile/dic/para/master/"
#define MULTIMASTER_IMAGE_FOLDER "../datafile/dic/para/multiMaster/"
#define CHANGE_DATA_FILE "../datafile/dic/para/points_displacement.txt"
#define VIDEO_DISPLACEMENT_FILE "../datafile/dic/para/video_displacement.txt"
#define LOG_FILE "../datafile/dic/logs/visual_log.txt"
#define ERROR_LOG_FILE "../datafile/dic/logs/visual_error.txt"
#define mqttDemo "./mqttDemo"
#define HIMIX_PUB_LOG_FILE "../datafile/dic/logs/himixPub.log"
#define LOG_DIR "../datafile/dic/logs"

#define CFG_INI "./cfg.ini" //设备配置文件
#define ACTIVATION_FILE "./activation.dat" //激活文件
#define TRIAL_PERIOD_DAYS 30 //试用期天数

//#define mqttDemo "/mnt/d/source/docker/himixProgram_v5/mqttDemo"

// 添加数据库路径定义
#define DB_PATH "../datafile/dic/result.db"
//添加脚本执行文件
#define REBOOT_SCRIPT "./runMQTT.sh"