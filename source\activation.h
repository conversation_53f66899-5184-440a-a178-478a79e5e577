#ifndef _ACTIVATION_H_
#define _ACTIVATION_H_

#include <time.h>
#include <stdbool.h>

// 激活状态枚举
enum ActivationStatus {
    STATUS_NEED_ACTIVATE = 0,    // 需要激活（试用期结束）
    STATUS_IN_TRIAL = 1,         // 试用期内
    STATUS_ACTIVATED = 2,        // 已激活且在有效期内
    STATUS_NEED_RENEWAL = 3,     // 需要续期（激活已过期）
    STATUS_PERMANENT = 4         // 永久激活
};

// 激活信息结构
typedef struct {
    char reg_code[32];     // 注册码
    time_t start_time;     // 安装时间
    time_t trial_end_time; // 试用期结束时间
    time_t active_time;    // 激活时间
    time_t expire_time;    // 激活过期时间
    int active_type;       // 激活类型（1-5年或0表示永久），1表示一年2表示两年3表示三年4表示四年5表示五年0表示永久
    int is_activated;      // 是否已激活 1表示已激活 0表示未激活
    char active_code[64];  // 激活码
} ActivationInfo;

// 函数声明
int check_activation_status(void);
int get_remaining_trial_days(void);
const char* get_reg_code(void);
const char* get_expiry_date_string(void);
int verify_activation_code(const char* active_code,int active_code_len);
char* get_activation_info(void);

#endif // _ACTIVATION_H_