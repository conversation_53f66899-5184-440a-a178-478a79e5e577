#include "file_monitor.h"
#include <stdio.h>
#include <stdlib.h>
#include <sys/inotify.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include <time.h>
#include "utils.h"
#define EVENT_SIZE  (sizeof(struct inotify_event))
#define EVENT_BUF_LEN (1024 * (EVENT_SIZE + 16))
#define MAX_MONITORS 10

// 定义监控实例结构体
struct file_monitor {
    int inotify_fd;
    int watch_fd;
    pthread_t thread;
    bool running;
    char *file_path;
    void (*callback)(const char *);
};

struct monitor_args {
    struct file_monitor *monitor;
};

// 前向声明
static void *monitor_file_changes(void *arg);

// 全局监控管理器
static struct {
    struct file_monitor *monitors[MAX_MONITORS];
    int count;
    pthread_mutex_t mutex;
} monitor_manager = {
    .count = 0,
    .mutex = PTHREAD_MUTEX_INITIALIZER
};

// 检查文件是否已经在监控中
static struct file_monitor* find_existing_monitor(const char *file_path) {
    pthread_mutex_lock(&monitor_manager.mutex);
    for (int i = 0; i < monitor_manager.count; i++) {
        if (monitor_manager.monitors[i] && 
            strcmp(monitor_manager.monitors[i]->file_path, file_path) == 0) {
            pthread_mutex_unlock(&monitor_manager.mutex);
            return monitor_manager.monitors[i];
        }
    }
    pthread_mutex_unlock(&monitor_manager.mutex);
    return NULL;
}

// 添加监控到管理器
static bool add_monitor_to_manager(struct file_monitor *monitor) {
    pthread_mutex_lock(&monitor_manager.mutex);
    if (monitor_manager.count >= MAX_MONITORS) {
        pthread_mutex_unlock(&monitor_manager.mutex);
        return false;
    }
    monitor_manager.monitors[monitor_manager.count++] = monitor;
    pthread_mutex_unlock(&monitor_manager.mutex);
    return true;
}

// 从管理器中移除监控
static void remove_monitor_from_manager(struct file_monitor *monitor) {
    pthread_mutex_lock(&monitor_manager.mutex);
    for (int i = 0; i < monitor_manager.count; i++) {
        if (monitor_manager.monitors[i] == monitor) {
            // 移动后面的元素前移
            for (int j = i; j < monitor_manager.count - 1; j++) {
                monitor_manager.monitors[j] = monitor_manager.monitors[j + 1];
            }
            monitor_manager.count--;
            break;
        }
    }
    pthread_mutex_unlock(&monitor_manager.mutex);
}

struct file_monitor* start_file_monitor(const char *file_path, void (*callback)(const char *)) {
    // 检查文件是否已经在监控中
    struct file_monitor *existing = find_existing_monitor(file_path);
    if (existing) {
 
        return existing;
    }

    struct file_monitor *monitor = malloc(sizeof(struct file_monitor));
    if (!monitor) {
        print_error_with_time("malloc failed");
        return NULL;
    }

    // 初始化结构体
    monitor->inotify_fd = -1;
    monitor->watch_fd = -1;
    monitor->file_path = NULL;
    monitor->running = false;


    monitor->inotify_fd = inotify_init();
    if (monitor->inotify_fd < 0) {
        print_error_with_time("inotify_init failed\n");
        free(monitor);
        return NULL;
    }


    monitor->watch_fd = inotify_add_watch(monitor->inotify_fd, file_path, IN_MODIFY | IN_DELETE_SELF);
    if (monitor->watch_fd < 0) {
        print_error_with_time("文件监听增加错误\n");
        close(monitor->inotify_fd);
        free(monitor);
        return NULL;
    }

    monitor->running = true;
    monitor->file_path = strdup(file_path);
    monitor->callback = callback;

    // 添加到管理器
    if (!add_monitor_to_manager(monitor)) {
    
        inotify_rm_watch(monitor->inotify_fd, monitor->watch_fd);
        close(monitor->inotify_fd);
        free(monitor->file_path);
        free(monitor);
        return NULL;
    }

    struct monitor_args *args = malloc(sizeof(struct monitor_args));
    if (!args) {
        print_error_with_time("malloc failed for args");
        remove_monitor_from_manager(monitor);
        inotify_rm_watch(monitor->inotify_fd, monitor->watch_fd);
        close(monitor->inotify_fd);
        free(monitor->file_path);
        free(monitor);
        return NULL;
    }
    args->monitor = monitor;

    if (pthread_create(&monitor->thread, NULL, monitor_file_changes, args) != 0) {
        print_error_with_time("pthread_create failed");
        remove_monitor_from_manager(monitor);
        inotify_rm_watch(monitor->inotify_fd, monitor->watch_fd);
        close(monitor->inotify_fd);
        free(monitor->file_path);
        free(monitor);
        free(args);
        return NULL;
    }

    return monitor;
}

void stop_file_monitor(struct file_monitor *monitor) {
    if (!monitor) return;
    monitor->running = false;
    
    remove_monitor_from_manager(monitor);
    
    if (monitor->watch_fd >= 0) {
        inotify_rm_watch(monitor->inotify_fd, monitor->watch_fd);
    }
    
    if (monitor->inotify_fd >= 0) {
        close(monitor->inotify_fd);
    }

    pthread_join(monitor->thread, NULL);
    free(monitor->file_path);
    free(monitor);
}

static void *monitor_file_changes(void *arg) {
    struct monitor_args *args = (struct monitor_args *)arg;
    struct file_monitor *monitor = args->monitor;
    char buffer[EVENT_BUF_LEN];
    time_t now;
    char time_str[64];

    while (monitor->running) {

        int length = read(monitor->inotify_fd, buffer, EVENT_BUF_LEN);
        if (length < 0) {
            print_error_with_time("read failed");
            break;
        }

        int i = 0;
        while (i < length) {
            struct inotify_event *event = (struct inotify_event *)&buffer[i];
            if (event->mask & IN_MODIFY) {
                time(&now);
                strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", localtime(&now));
              
                if (monitor->callback) monitor->callback(monitor->file_path);
            }
            if (event->mask & IN_DELETE_SELF) {
                time(&now);
                strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", localtime(&now));
                if (monitor->callback) monitor->callback(monitor->file_path);
                monitor->watch_fd = inotify_add_watch(monitor->inotify_fd, 
                                                    monitor->file_path, 
                                                    IN_MODIFY | IN_DELETE_SELF);
                if (monitor->watch_fd < 0) {
                    sleep(1);
                    continue;
                }
            }
            i += EVENT_SIZE + event->len;
        }
    }
    free(args);
    return NULL;
}
