#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>

// Base64字符表
const char base64_table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

// 函数：将二进制数据转换为Base64编码
char* base64_encode(const unsigned char *data, size_t input_length, size_t *output_length) {
    *output_length = 4 * ((input_length + 2) / 3); // 计算Base64编码后的长度
    char *encoded_data = (char *)malloc(*output_length);
    if (encoded_data == NULL) {
        return NULL;
    }

    for (size_t i = 0, j = 0; i < input_length;) {
        uint32_t octet_a = i < input_length ? data[i++] : 0;
        uint32_t octet_b = i < input_length ? data[i++] : 0;
        uint32_t octet_c = i < input_length ? data[i++] : 0;


        uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;

        encoded_data[j++] = base64_table[(triple >> 3 * 6) & 0x3F];
        encoded_data[j++] = base64_table[(triple >> 2 * 6) & 0x3F];
        encoded_data[j++] = base64_table[(triple >> 1 * 6) & 0x3F];
        encoded_data[j++] = base64_table[(triple >> 0 * 6) & 0x3F];
    }

    // 处理Base64编码后的末尾填充字符
    for (size_t i = 0; i < (3 - input_length % 3) % 3; i++) {
        encoded_data[*output_length - 1 - i] = '=';
    }
    return encoded_data;
}

char* image_to_base64(const char* image_path, size_t* output_size) {
    FILE* fp = fopen(image_path, "rb");
    if (!fp) {
        print_with_time("无法打开图片文件: %s\n", image_path);
        return NULL;
    }
    
    // 获取文件大小
    fseek(fp, 0, SEEK_END);
    long file_size = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    
    // 分配内存读取文件
    unsigned char* file_data = malloc(file_size);
    if (!file_data) {
        print_error_with_time("内存分配失败\n");
        fclose(fp);
        return NULL;
    }
    
    // 读取文件
    size_t bytes_read = fread(file_data, 1, file_size, fp);
    fclose(fp);
    
    if (bytes_read != file_size) {
        print_error_with_time("读取文件失败，预期: %ld, 实际: %zu\n", file_size, bytes_read);
        free(file_data);
        return NULL;
    }
    
    // 直接进行Base64编码
    char* encoded_data = base64_encode(file_data, file_size, output_size);
    free(file_data);
    
    if (!encoded_data) {
        print_error_with_time("Base64编码失败\n");
        return NULL;
    }
    return encoded_data;
}

// 将base64字符转换为对应的十进制值
static int64_t base64_decode_char(char c) {
    if (c >= 'A' && c <= 'Z') return c - 'A';
    if (c >= 'a' && c <= 'z') return c - 'a' + 26;
    if (c >= '0' && c <= '9') return c - '0' + 52;
    if (c == '+') return 62;
    if (c == '/') return 63;
    return -1;
}

// 解码base64字符串
size_t base64_decode_len(const char *src, size_t src_len, unsigned char *target) {
    size_t len = 0;
    int i = 0;
    int shift = 0;
    uint32_t buffer = 0;
    
    for (i = 0; i < src_len; i++) {
        int64_t value = base64_decode_char(src[i]);
        if (value < 0) continue;
        
        buffer = (buffer << 6) | value;
        shift += 6;
        
        if (shift >= 8) {
            shift -= 8;
            target[len++] = (buffer >> shift) & 0xFF;
        }
    }
    
    return len;//返回解码后的数据长度
}
unsigned char* base64_decode(const char* input, int* out_len) {
    int len = strlen(input);
    int padding = 0;
    if(len > 0 && input[len - 1] == '=') padding++;
    if(len > 1 && input[len - 2] == '=') padding++;

    int max_decoded_len = (len * 3) / 4;
    unsigned char *decoded = malloc(max_decoded_len);
    if (!decoded) {
        fprintf(stderr, "内存分配失败\n");
        exit(1);
    }
    // EVP_DecodeBlock 返回的长度包含了填充部分
    int decoded_len = EVP_DecodeBlock(decoded, (const unsigned char*)input, len);
    if (decoded_len < 0) {
        free(decoded);
        fprintf(stderr, "Base64 解码失败\n");
        exit(1);
    }
    // 减去填充字符产生的多余字节
    *out_len = decoded_len - padding;
    return decoded;
}
